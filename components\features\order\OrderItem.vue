<template>
  <div
    class="bg-card relative text-card-foreground p-2 rounded-lg border flex items-center overflow-hidden text-sm"
    :class="{ 'opacity-50': !isChecked }"
  >
    <input
      type="checkbox"
      id="choose-me"
      v-model="isChecked"
      @click="toggleCheck"
      class="w-4 h-4 accent-primary rounded-full cursor-pointer"
    />
    <label for="product1" class="cursor-pointer flex items-center w-full ml-2">
      <NuxtImg
        :src="image || 'https://placehold.co/80'"
        alt="Product Image"
        class="flex-shrink-0 w-16 h-16 rounded-lg object-contain"
        loading="lazy"
        preload
      />
      <div class="ml-4 flex-1">
        <div class="flex items-center justify-between">
          <h3 class="text-sm line-clamp-2 font-semibold">
            {{
              `${product?.variant.title} (#${product?.variant?.id}${
                product?.variant?.sku ? ` - SKU: ${product?.variant?.sku}` : ``
              })`
            }}
          </h3>
        </div>

        <div class="flex items-center justify-between mt-2">
          <div class="flex items-center gap-1 md:w-[250px]">
            <span
              :class="
                discountValue > 0
                  ? 'line-through font-bold text-red-500'
                  : 'font-bold text-primary'
              "
            >
              {{
                formatCurrency(
                  product.originalTotalPrice.amount +
                    ((product?.vatRate?.amount *
                      product.originalTotalPrice.amount) /
                      100 || 0)
                )
              }}
            </span>
            <span v-if="discountValue > 0" class="text-primary font-bold">
              {{
                formatCurrency(
                  product?.discountedTotalPrice?.amount +
                    (product?.totalVAT?.amount || 0)
                )
              }}
            </span>
          </div>

          <!--  -->
          <div class="md:block hidden">
            <div
              class="flex items-center border border-gray-300 rounded-lg px-2 py-1 max-w-[130px]"
            >
              <select
                v-model="discountType"
                class="focus:outline-none bg-transparent"
                @change="handleDiscountTypeChange"
              >
                <option value="MONEY">₫</option>
                <option value="PERCENT">%</option>
              </select>
              <input
                type="text"
                class="w-full focus:outline-none text-right"
                v-model="discountValue"
                @input="formatCurrencyV2"
                @blur="handleBlur"
              />
            </div>
          </div>

          <!--  -->
          <div class="flex items-center">
            <button
              v-if="!isDecreaseQuantity"
              @click.prevent.stop="decreaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
            >
              -
            </button>
            <button
              v-else
              @click.prevent.stop="handleQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
            >
              -
            </button>
            <input
              type="number"
              min="1"
              v-model="product.currentQuantity"
              @input="updateTotalPrice"
              @blur="updateQuantity"
              class="w-10 h-6 text-center border border-input rounded-md mx-2"
            />
            <button
              @click.prevent.stop="increaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary-foreground rounded-md"
            >
              +
            </button>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center gap-1">
            <div class="flex items-center gap-1">
              <span class="text-sm line-clamp-2 font-semibold">Kho:</span>
              <span
                v-if="dataProduct?.orderAble > 0"
                class="text-sm line-clamp-2 font-semibold"
              >
                {{ dataProduct?.orderAble }}
              </span>
              <span
                v-else-if="dataProduct?.orderAble <= 0"
                class="text-sm line-clamp-2 font-semibold text-red-500"
              >
                Hết hàng
              </span>
              <span
                v-else
                class="text-sm line-clamp-2 font-semibold text-orange-500"
              >
                Chưa lưu kho
              </span>
            </div>
            <div
              @click="handleNavigate"
              class="flex items-center gap-1 text-primary border-b-[1px] border-primary"
            >
              <span>Kiểm tra kho</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"
                />
              </svg>
            </div>
          </div>
          <div @click="handleOpenEditProduct" class="block md:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
              />
            </svg>
          </div>
        </div>
      </div>
    </label>
    <div
      v-if="isUpdating"
      class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center"
    >
      <svg
        class="animate-spin h-5 w-5 text-primary"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0c4.418 0 8 3.582 8 8h-2c0-3.314-2.686-6-6-6V0c-3.314 0-6 2.686-6 6H4z"
        ></path>
      </svg>
    </div>
  </div>
  <!-- <div @click="handleCancel()"> test</div> -->
  <ConfirmDialog
    v-if="isDialogVisible"
    :title="'Xác nhận'"
    :message="'Bạn có chắc chắn muốn xoá sản phẩm này?'"
    @confirm="removeFromCartConfirmed"
    @cancel="handleCancel"
  />
  <EditProductPopup
    v-if="isOpenEditProduct"
    @confirm="handleOpenEditProduct"
    @cancel="handleOpenEditProduct"
    :product="product"
  >
  </EditProductPopup>
  <ConfirmDialog
    v-if="isEditOrderIncrease"
    :title="`Thông báo`"
    :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh sản phẩm`"
    @confirm="confirmIncrease"
    @cancel="cancelIncrease"
  ></ConfirmDialog>
  <ConfirmDialog
    v-if="isEditOrderDecrease"
    :title="`Thông báo`"
    :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh sản phẩm`"
    @confirm="confirmDecrease"
    @cancel="cancelDecrease"
  ></ConfirmDialog>
  <ConfirmDialog
    v-if="isEditOrderDisCount"
    :title="`Thông báo`"
    :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh chiết khấu sản phẩm`"
    @confirm="confirmDiscount"
    @cancel="cancelDiscount"
  ></ConfirmDialog>
  <ConfirmDialog
    v-if="isEditOrderTypeDisCount"
    :title="`Thông báo`"
    :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh chiết khấu sản phẩm`"
    @confirm="confirmTypeDiscount"
    @cancel="cancelTypeDiscount"
  ></ConfirmDialog>
</template>

<script setup>
const ConfirmDialog = defineAsyncComponent(() =>
  import("~/components/dialog/ConfirmDialog.vue")
);
const EditProductPopup = defineAsyncComponent(() =>
  import("~/components/ui/feedback/EditProductPopup.vue")
);
import debounce from "lodash/debounce";
const productStore = useProductStore();
const props = defineProps({
  product: Object,
  isCall: Boolean,
});
const { updateDiscountPriceInOrder } = useOrder();
const discountType = ref("MONEY");
const discountValue = ref(props.product?.discount?.value?.amount || 0);
const originalPrice = ref(props.product?.amount);
const isDialogVisible = ref(false);
const isChecked = ref(true);
const orderStore = useOrderStore();

const isUpdating = ref(false);
const image = ref("");
const route = useRoute();
const isDecreaseQuantity = ref(false);
const app = useNuxtApp();
const handleQuantity = () => {
  app.$toast.warning("Sản phẩm có số lượng là 1, không thể giảm");
};
//
const isEditOrderIncrease = ref(false);
const isEditOrderDecrease = ref(false);

//
const handleCancel = () => {
  isDialogVisible.value = false;
  isChecked.value = true; // Đặt lại giá trị về true
};
const increaseQuantity = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderIncrease.value = true;
  } else {
    isUpdating.value = true;
    const newQuantity = props.product.currentQuantity + 1;
    await orderStore.updateQuantity(props.product.id, newQuantity);
    isUpdating.value = false;
  }
};
const decreaseQuantity = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  if (props.product.currentQuantity > 1) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderDecrease.value = true;
    } else {
      isUpdating.value = true;
      const newQuantity = props.product.currentQuantity - 1;
      await orderStore.updateQuantity(props.product.id, newQuantity);
      isUpdating.value = false;
    }
  } else {
    isDecreaseQuantity.value = true;
  }
};
//
const confirmIncrease = async () => {
  isUpdating.value = true;
  const newQuantity = props.product.currentQuantity + 1;
  await orderStore.updateQuantity(props.product.id, newQuantity);
  isUpdating.value = false;
  cancelIncrease();
};
const cancelIncrease = () => {
  isEditOrderIncrease.value = false;
};
//
const confirmDecrease = async () => {
  isUpdating.value = true;
  const newQuantity = props.product.currentQuantity - 1;
  await orderStore.updateQuantity(props.product.id, newQuantity);
  isUpdating.value = false;
  cancelDecrease();
};
const cancelDecrease = () => {
  isEditOrderDecrease.value = false;
};
// watch(
//   () => props.product.currentQuantity,
//   (newVal, oldVal) => {
//     if (newVal !== oldVal) {
//       isUpdating.value = false;
//     }
//   }
// );
watch(
  () => props.product.currentQuantity,
  (newVal, oldVal) => {
    if (newVal <= 1) {
      isDecreaseQuantity.value = true;
    } else {
      isDecreaseQuantity.value = false;
    }
  }
);

const dataProduct = ref();
const { getImageProducrUrl } = usePortal();
onMounted(async () => {
  const url = getImageProducrUrl(props.product?.variant?.id, "PRODUCT");
  image.value = url;
  if (props.product.currentQuantity === 1) {
    isDecreaseQuantity.value = true;
  }
  dataProduct.value = await orderStore.handleCheckInventory(props.product);
});

const updateTotalPrice = () => {
  originalPrice.value = props.product.amount * props.product.currentQuantity;
};

const showRemoveDialog = () => {
  isDialogVisible.value = true;
};
const orderDetail = computed(() => orderStore.orderDetail);

const removeFromCartConfirmed = async () => {
  const result = await orderStore.handleRemoveProduct(
    route.query.orderId,
    props.product?.id
  );
  if (result?.status === 0) {
    isChecked.value = !isChecked.value;
  } else {
    isDialogVisible.value = false;
  }
};

const handleRemoveProduct = () => {
  showRemoveDialog();
};
// watch(
//   () => isChecked.value,
//   (newVal, oldVal) => {
//     console.log("newVal", newVal);
//     if (newVal === false) {
//       showRemoveDialog();
//     }
//   }
// );
const toggleCheck = async (event) => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );

    return;
  }
  // event.stopPropagation();
  isChecked.value = !isChecked.value;
  if (isChecked.value) {
  } else {
    showRemoveDialog();
  }
};
//
const isEditOrderDisCount = ref(false);
const handleBlur = async () => {
  if (discountType.value === "PERCENT" && discountValue.value > 100) {
    discountValue.value = 100; // Đặt giá trị tối đa là 100
    // app.$toast.warning("Chiết khấu phần trăm không thể lớn hơn 100%");
  }

  if (
    discountType.value === "MONEY" &&
    discountValue.value > props.product.originalTotalPrice.amount
  ) {
    discountValue.value = props.product.originalTotalPrice.amount; // Đặt giá trị tối đa là giá sản phẩm
    // app.$toast.warning("Chiết khấu tiền không thể lớn hơn giá sản phẩm");
  }

  if (discountValue.value >= 0) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderDisCount.value = true;
    } else {
      const data = {
        type_discount: discountType.value,
        discount_amount: discountValue.value,
        campaign_id: "",
        campaign_action_id: "",
        campaign_action_type: "",
      };
      await updateDiscountPriceInOrder(
        props.product?.order_id,
        props.product?.id,
        data
      );
      await orderStore.getOrderById(props.product?.order_id);
    }
  }
};

const confirmDiscount = async () => {
  const data = {
    type_discount: discountType.value,
    discount_amount: discountValue.value,
    campaign_id: "",
    campaign_action_id: "",
    campaign_action_type: "",
  };
  await updateDiscountPriceInOrder(
    props.product?.order_id,
    props.product?.id,
    data
  );
  await orderStore.getOrderById(props.product?.order_id);
  cancelDiscount();
};
const cancelDiscount = () => {
  isEditOrderDisCount.value = false;
};
const isEditOrderTypeDisCount = ref(false);
// watch(
//   () => discountType.value,
//   async (newVal, oldVal) => {
//     if (newVal) {
//       if (orderDetail.value?.status === "APPROVED") {
//         isEditOrderTypeDisCount.value = true;
//       } else {
//         discountType.value = newVal;
//         discountValue.value = 0;
//         const data = {
//           type_discount: discountType.value,
//           discount_amount: discountValue.value,
//           campaign_id: "",
//           campaign_action_id: "",
//           campaign_action_type: "",
//         };
//         await updateDiscountPriceInOrder(
//           props.product?.order_id,
//           props.product?.id,
//           data
//         );
//         await orderStore.getOrderById(props.product?.order_id);
//       }
//     }
//   }
// );
//

const handleDiscountTypeChange = async () => {
  // Lưu giá trị hiện tại trước khi thay đổi

  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderTypeDisCount.value = true;
  } else {
    discountValue.value = 0;
    const data = {
      type_discount: discountType.value,
      discount_amount: discountValue.value,
      campaign_id: "",
      campaign_action_id: "",
      campaign_action_type: "",
    };
    await updateDiscountPriceInOrder(
      props.product?.order_id,
      props.product?.id,
      data
    );
    await orderStore.getOrderById(props.product?.order_id);
  }
};

//
const confirmTypeDiscount = async () => {
  discountValue.value = 0;
  const data = {
    type_discount: discountType.value,
    discount_amount: discountValue.value,
    campaign_id: "",
    campaign_action_id: "",
    campaign_action_type: "",
  };
  await updateDiscountPriceInOrder(
    props.product?.order_id,
    props.product?.id,
    data
  );
  await orderStore.getOrderById(props.product?.order_id);
  isEditOrderTypeDisCount.value = false;
};
const cancelTypeDiscount = () => {
  // Chuyển đổi loại discount
  if (discountType.value === "MONEY") {
    discountType.value = "PERCENT";
  } else {
    discountType.value = "MONEY"; // Nếu không phải MONEY thì đặt lại là MONEY
  }

  isEditOrderTypeDisCount.value = false;
};

// watch(
//   () => discountValue.value,
//   async (newVal, oldVal) => {
//     discountValue.value = newVal;
//     if (newVal > 0) {
//       const data = {
//         type_discount: discountType.value,
//         discount_amount: discountValue.value,
//         campaign_id: "",
//         campaign_action_id: "",
//         campaign_action_type: "",
//       };
//       await updateDiscountPriceInOrder(
//         props.product?.order_id,
//         props.product?.id,
//         data
//       );
//       await orderStore.getOrderById(props.product?.order_id);
//     }
//   }
// );
const isOpenEditProduct = ref(false);
const handleOpenEditProduct = () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  isOpenEditProduct.value = !isOpenEditProduct.value;
};
const { getUrlWarehousePortal } = usePortal();
const handleNavigate = () => {
  const url = getUrlWarehousePortal(
    props.product.variant?.sku,
    props.product?.variant?.id
  );
  console.log("url", url);

  if (url) {
    window.open(url, "_blank"); // Mở link trong tab mới
  }
};
</script>
