<template>
  <div class="h-full mx-2">
    <div class="mt-2">
      <SearchDiary
        class="mb-2 md:mb-0"
        @handleSearch="handleSearch"
      ></SearchDiary>
      <div v-if="isAlert" class="text-sm text-red-500">
        Không tìm thấy đơn hàng
      </div>
    </div>

    <!-- Loading states -->
    <LoadingDiary v-if="isLoading"></LoadingDiary>
    <div
      v-if="loading || loadingCreateOrder || loadingNavigate || isLoadingPage"
    >
      <LoadingSpinner />
    </div>

    <div class="border-b-gray-200 relative">
      <div v-if="diaries?.length" class="flex flex-col gap-2 mb-4">
        <!-- Desktop table view -->
        <div class="bg-white md:block hidden">
          <table class="table-auto w-full text-sm">
            <thead>
              <tr class="bg-blue-100 text-left font-semibold">
                <th class="p-2 w-1/12 text-center"><PERSON><PERSON> đơn</th>
                <th class="p-2 w-2/12"><PERSON>h<PERSON><PERSON> hàng</th>
                <th class="p-2 w-2/12">Nhật ký</th>
                <th class="p-2 w-2/12">Nhân viên</th>
                <th class="p-2 w-5/12 max-w-0">Sản phẩm quan tâm</th>
                <th class="p-2 w-full">
                  <span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="size-5"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                      />
                    </svg>
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="diary in diaries"
                :key="diary.id"
                class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white"
              >
                <TableDiary
                  :diary="diary"
                  :data="data"
                  @handleLoading="toggleLoading"
                ></TableDiary>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Mobile card view -->
        <div class="block md:hidden">
          <CardDiary
            v-for="diary in diaries"
            :key="diary.id"
            :diary="diary"
            :data="data"
            @handleLoading="toggleLoading"
          />
        </div>

        <!-- Pagination -->
        <div
          v-if="pagination.totalItems > 0"
          class="mt-6 bg-white rounded-lg border border-gray-200 p-4"
        >
          <Pagination
            :current-page="pagination.currentPage"
            :total-pages="Math.max(1, pagination.totalPages)"
            :total-items="pagination.totalItems"
            :items-per-page="pagination.itemsPerPage"
            :show-page-size-selector="true"
            :page-size-options="[10, 20, 50]"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </div>
      </div>

      <!-- Empty state -->
      <div v-if="!diaries?.length && !isLoading">
        <div
          class="flex items-center justify-center font-bold text-primary bg-white h-screen"
        >
          Hiện tại chưa có nhật ký!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Component imports
const CardDiary = defineAsyncComponent(() =>
  import("~/components/features/diary/CardDiary.vue")
);
const TableDiary = defineAsyncComponent(() =>
  import("~/components/features/diary/TableDiary.vue")
);
const SearchDiary = defineAsyncComponent(() =>
  import("~/components/features/diary/SearchDiary.vue")
);
const LoadingDiary = defineAsyncComponent(() =>
  import("~/components/ui/feedback/LoadingDiary.vue")
);
const LoadingSpinner = defineAsyncComponent(() =>
  import("~/components/shared/common/LoadingSpinner.vue")
);
const Pagination = defineAsyncComponent(() =>
  import("~/components/ui/navigation/Pagination.vue")
);

import { ref, reactive, onMounted, computed } from "vue";

// Stores and composables
const orderStore = useOrderStore();
const loading = computed(() => orderStore.loading);
const { fetchListSellOrder } = useOrder();

// Page meta
useHead({
  title: "Nhật ký bán hàng",
  meta: [{ name: "description", content: "Bán hàng" }],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN"],
  name: "Nhật ký bán hàng",
});

// Reactive state
const isLoading = ref(true);
const isAlert = ref(false);
const isLoadingPage = ref(false);
const loadingNavigate = ref(false);
const loadingCreateOrder = ref(false);
const diaries = ref([]);

// Pagination state
const pagination = reactive({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 10,
});

// Search and filter options
const searchOptions = reactive({
  date_create_to: null,
  date_create_from: null,
  keyword: "",
});

// API request options
const options = computed(() => ({
  source: "diary",
  currentPage: pagination.currentPage,
  maxResult: pagination.itemsPerPage,
  ...searchOptions,
}));

// Methods
const fetchDiaries = async () => {
  isAlert.value = false;
  isLoading.value = true;

  try {
    console.log("Fetching diaries with options:", options.value);
    const response = await fetchListSellOrder(options.value);
    console.log("Full API Response:", JSON.stringify(response, null, 2));

    // Initialize safe defaults
    let responseData = [];
    let totalCount = 0;

    // Handle response structure safely
    if (response && typeof response === "object") {
      console.log("Response structure:", {
        hasData: !!response.data,
        dataType: typeof response.data,
        isDataArray: Array.isArray(response.data),
        hasNestedData:
          response.data &&
          typeof response.data === "object" &&
          "data" in response.data,
        totalCount: response.totalCount,
        dataTotalCount: response.data?.totalCount,
        dataTotal: response.data?.total,
      });

      if (response.data) {
        if (Array.isArray(response.data)) {
          // Case 1: response.data is directly an array
          responseData = response.data;
          totalCount = response.totalCount || response.data.length;
        } else if (response.data && typeof response.data === "object") {
          if (Array.isArray(response.data.data)) {
            // Case 2: response.data.data is an array
            responseData = response.data.data;
            totalCount =
              response.data.totalCount ||
              response.data.total ||
              response.data.data.length;
          } else {
            // Case 3: response.data is an object but not with nested data array
            console.warn("Unexpected response structure:", response.data);
            responseData = [];
            totalCount = 0;
          }
        }
      }
    }

    // Ensure we have valid data
    diaries.value = Array.isArray(responseData) ? responseData : [];

    // Safely handle totalCount
    const safeTotalCount =
      typeof totalCount === "number" && !isNaN(totalCount) ? totalCount : 0;

    // Update pagination info with safe defaults
    pagination.totalItems = Math.max(0, safeTotalCount);
    pagination.totalPages =
      pagination.totalItems > 0
        ? Math.ceil(pagination.totalItems / pagination.itemsPerPage)
        : 1;

    // Ensure current page is within valid range
    if (pagination.currentPage > pagination.totalPages) {
      pagination.currentPage = Math.max(1, pagination.totalPages);
    }

    console.log("Processed data:", {
      diariesCount: diaries.value.length,
      totalItems: pagination.totalItems,
      totalPages: pagination.totalPages,
      currentPage: pagination.currentPage,
    });

    if (diaries.value.length === 0 && pagination.totalItems === 0) {
      isAlert.value = true;
    }
  } catch (error) {
    console.error("Error fetching diaries:", error);
    console.error("Error details:", error.message, error.stack);
    isAlert.value = true;
    diaries.value = [];
    // Reset pagination on error
    pagination.totalItems = 0;
    pagination.totalPages = 1;
    pagination.currentPage = 1;
  } finally {
    isLoading.value = false;
  }
};

const handlePageChange = async (page) => {
  pagination.currentPage = page;
  await fetchDiaries();
};

const handlePageSizeChange = async (size) => {
  pagination.itemsPerPage = size;
  pagination.currentPage = 1; // Reset to first page when changing page size
  await fetchDiaries();
};

const handleSearch = async (data) => {
  isLoadingPage.value = true;

  // Update search options
  searchOptions.date_create_to = data?.date_create_to;
  searchOptions.date_create_from = data?.date_create_from;
  searchOptions.keyword = data?.keyword;

  // Reset to first page for new search
  pagination.currentPage = 1;

  await fetchDiaries();
  isLoadingPage.value = false;
};

const toggleLoading = (state) => {
  loadingNavigate.value = state;
};

// Lifecycle
onMounted(async () => {
  await fetchDiaries();
});

// Data
const { data } = await useFetch("/data/setting.json");
</script>

<style scoped>
.h-full {
  height: 100%;
}
</style>
