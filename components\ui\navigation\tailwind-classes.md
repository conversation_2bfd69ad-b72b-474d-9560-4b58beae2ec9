# Tailwind CSS Classes Used in Diaries Page

## Height Calculations
```html
<!-- Main container with calculated height -->
<div class="h-[calc(100vh-56px)] max-h-[calc(100vh-56px)] flex flex-col mx-2">

<!-- Responsive height for mobile -->
@media (max-width: 768px) {
  .h-[calc(100vh-56px)] {
    height: calc(100vh - 56px - env(safe-area-inset-top, 0px));
  }
}
```

## Layout Classes
```html
<!-- Flexbox layout -->
<div class="flex flex-col">           <!-- Vertical flex container -->
<div class="flex-1">                 <!-- Takes available space -->
<div class="flex-shrink-0">          <!-- Prevents shrinking -->
<div class="min-h-0">                <!-- Allows flex children to shrink -->

<!-- Positioning -->
<div class="sticky top-0 z-10">     <!-- Sticky header -->
<div class="relative">               <!-- Relative positioning -->
<div class="absolute">               <!-- Absolute positioning -->
```

## Scrolling Classes
```html
<!-- Custom scrollbar -->
<div class="overflow-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">

<!-- Scrollbar utilities -->
.scrollbar-thin                     <!-- Thin scrollbar -->
.scrollbar-thumb-gray-300           <!-- Scrollbar thumb color -->
.scrollbar-track-gray-100           <!-- Scrollbar track color -->
```

## Spacing Classes
```html
<!-- Margins and Padding -->
<div class="mx-2">                  <!-- Horizontal margin -->
<div class="mt-4">                  <!-- Top margin -->
<div class="p-4">                   <!-- All padding -->
<div class="pt-2 pb-4">             <!-- Top and bottom padding -->

<!-- Gaps -->
<div class="space-y-2">             <!-- Vertical spacing between children -->
<div class="gap-2">                 <!-- Gap in flex/grid -->
```

## Visual Classes
```html
<!-- Background and Borders -->
<div class="bg-white">              <!-- White background -->
<div class="bg-blue-100">           <!-- Light blue background -->
<div class="border border-gray-200"> <!-- Gray border -->
<div class="rounded-lg">            <!-- Rounded corners -->

<!-- Shadows -->
<div class="shadow-lg">             <!-- Large shadow -->
<div class="shadow-sm">             <!-- Small shadow -->
```

## Typography Classes
```html
<!-- Text styling -->
<div class="text-sm">               <!-- Small text -->
<div class="text-xl">               <!-- Extra large text -->
<div class="font-bold">             <!-- Bold font -->
<div class="font-semibold">         <!-- Semi-bold font -->

<!-- Text colors -->
<div class="text-gray-500">         <!-- Gray text -->
<div class="text-primary">          <!-- Primary color text -->
<div class="text-red-500">          <!-- Red text -->
```

## Animation Classes
```html
<!-- Transitions -->
<div class="transition-all duration-300 ease-in-out">
<div class="transition-colors duration-200">

<!-- Animations -->
<div class="animate-bounce">         <!-- Bounce animation -->
<div class="animate-pulse">          <!-- Pulse animation -->
<div class="animate-fade-in">        <!-- Custom fade-in animation -->

<!-- Hover effects -->
<div class="hover:bg-gray-50">       <!-- Hover background -->
<div class="hover:text-white">       <!-- Hover text color -->
```

## Responsive Classes
```html
<!-- Breakpoint prefixes -->
<div class="hidden md:block">        <!-- Hidden on mobile, block on desktop -->
<div class="block md:hidden">        <!-- Block on mobile, hidden on desktop -->
<div class="sm:flex">                <!-- Flex on small screens and up -->
<div class="lg:text-lg">             <!-- Large text on large screens -->

<!-- Responsive spacing -->
<div class="p-4 md:p-6">             <!-- Different padding on different screens -->
<div class="text-sm md:text-base">   <!-- Responsive text size -->
```

## State Classes
```html
<!-- Interactive states -->
<button class="disabled:opacity-50 disabled:cursor-not-allowed">
<button class="focus:ring-2 focus:ring-blue-500">
<button class="active:scale-95">

<!-- Loading states -->
<div class="opacity-50">             <!-- Semi-transparent -->
<div class="cursor-not-allowed">     <!-- Disabled cursor -->
```

## Grid and Table Classes
```html
<!-- Table styling -->
<table class="table-auto w-full">    <!-- Auto table layout, full width -->
<th class="text-left">               <!-- Left-aligned header -->
<tr class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white">

<!-- Width utilities -->
<div class="w-1/12">                 <!-- 1/12 width -->
<div class="w-full">                 <!-- Full width -->
<div class="max-w-0">                <!-- Max width 0 (for truncation) -->
```

## Custom Utilities
```css
/* Custom height calculations */
.h-[calc(100vh-56px)] {
  height: calc(100vh - 56px);
}

.max-h-[calc(100vh-56px)] {
  max-height: calc(100vh - 56px);
}

/* Custom animations */
@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}
```

## Accessibility Classes
```html
<!-- Screen reader utilities -->
<span class="sr-only">              <!-- Screen reader only -->
<div class="focus:outline-none focus:ring-2">

<!-- ARIA and semantic classes -->
<nav role="navigation" aria-label="Pagination Navigation">
<button aria-label="Go to next page">
```
