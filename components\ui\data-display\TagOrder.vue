<template>
  <div class="bg-white px-2 pb-2 rounded-lg">
    <div class="flex items-center justify-between">
      <span class="text-sm font-semibold text-primary"><PERSON><PERSON><PERSON><PERSON> ký</span>
      <span class="cursor-pointer">
        <svg
          v-if="isOpen"
          @click="handleOpenTag"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m4.5 15.75 7.5-7.5 7.5 7.5"
          />
        </svg>
        <svg
          v-else
          @click="handleOpenTag"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m19.5 8.25-7.5 7.5-7.5-7.5"
          />
        </svg>
      </span>
    </div>
    <div
      v-if="isOpen"
      v-for="tag in dataConnector"
      :key="tag._id"
      class="space-y-2"
    >
      <TagArea :tag="tag"></TagArea>
    </div>
    <button
      v-if="isOpen"
      @click="handleCreateConnection"
      class="text-sm flex items-center cursor-pointer gap-2 text-primary border border-primary px-2 py-1 rounded mt-1"
    >
      <span>+Thêm</span>
    </button>
  </div>
</template>
<script setup lang="ts">
// Lazy load heavy components
const TagArea = defineAsyncComponent(
  () => import("~/components/Tag/TagArea.vue")
);
import type { Auth } from "~/types/Auth";
const {
  searchTag,
  createTag,
  getConnectorByResource,
  createConnector,
  updateConnectorDescription,
} = usePortal();
const route = useRoute();
const tagStore = useTagStore();
const dataConnector = computed(() => tagStore.dataConnector);
const handleCreateConnection = async () => {
  await createConnector(route.query.orderId as string, "ORDER", "", "TAG", "");
  await tagStore.handleGetConnectorByResource(
    route.query.orderId as string,
    "ORDER",
    "TAG"
  );
};
const orderStore = useOrderStore();
const isNotDraft = computed(() => orderStore.isNotDraft);
const TagDescription = ref<string>("");
const tagTitle = ref<string>("");
const addTag = ref<any>([]);
const handleAddTag = (search: any) => {
  tagTitle.value = "";
  dataSearchTag.value = [];
  addTag.value = [...addTag.value, search];
};
const handleClearInput = () => {
  tagTitle.value = "";
  dataSearchTag.value = [];
};
const dataSearchTag = ref();

const handleSearchTag = async (keyword: string) => {
  try {
    const response = await searchTag("", "", "", keyword);
    dataSearchTag.value = response;
  } catch (error) {
    throw error;
  }
};
const handleCreateTag = async () => {
  const auth = useCookie("auth").value as unknown as Auth;

  const res = await createTag(tagTitle.value.slice(1), auth.user?.id, "");
  if (res) {
    addTag.value = [...addTag.value, res];
    tagTitle.value = "";
  }
};
let debounceTimer: any = null;
watch(
  () => tagTitle.value,
  (newValue: string) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(async () => {
      let searchValue = newValue.trim();
      if (searchValue.startsWith("#")) {
        searchValue = searchValue.slice(1).trim();
        await handleSearchTag(searchValue);
      }
    }, 600);
  }
);
const isOpen = ref(false);
const handleOpenTag = () => {
  isOpen.value = !isOpen.value;
};
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/diary`) {
    isOpen.value = true;
  }
  if (to.path === `/sale`) {
    isOpen.value = false;
  }
  next();
});
onMounted(() => {
  if (route.path === "/diary") {
    isOpen.value = true;
  }
  if (route.path === "/sale") {
    isOpen.value = false;
  }
});
</script>
