<template>
  <div class="rounded md:border-t p-1">
    <div class="bg-white w-full md:max-h-[25vh] px-2">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <h3 class="font-semibold text-primary text-sm"><PERSON><PERSON><PERSON> sử thanh toán</h3>
        <div @click="toggleVisible" class="cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 transition-transform duration-300"
            :class="{ 'rotate-180': isVisible }"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m19.5 8.25-7.5 7.5-7.5-7.5"
            ></path>
          </svg>
        </div>
      </div>
      <ModalConfirmPayment
        v-if="isModalOpen"
        :isEmployee="false"
        :paymentId="paymentId"
        :isOpen="isModalOpen"
        @update:isOpen="isModalOpen = $event"
        @confirm="handleConfirm"
      />

      <!-- Nội dung -->
      <transition name="slide-in">
        <div v-show="isVisible" class="overflow-y-auto md:max-h-[15vh]">
          <div v-if="loading" class="text-center text-gray-500">
            Đang tải...
          </div>
          <div
            v-else-if="listPayment?.length === 0"
            class="text-center text-gray-500"
          >
            Không có lịch sử thanh toán
          </div>
          <div v-else class="w-full">
            <!-- Desktop Table -->
            <table class="w-full border-collapse hidden md:table">
              <thead class="text-sm bg-blue-100">
                <tr>
                  <th class="px-4 py-2">Thời gian</th>
                  <th class="px-4 py-2">Phương thức</th>
                  <th class="px-4 py-2">Trạng thái</th>
                  <th class="px-4 py-2">Mã giao dịch</th>
                  <th class="px-4 py-2">Số tiền</th>
                  <th class="px-4 py-2">Ghi chú</th>
                  <th class="px-4 py-2">Xntt</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="payment in listPayment"
                  :key="payment.paymentId"
                  class="hover:bg-gray-100 text-sm"
                >
                  <td class="py-2 text-center">
                    {{ formatTimestampV3(payment?.payDate) }}
                  </td>
                  <td class="py-2 text-center">
                    {{ payment?.methodDescription }}
                  </td>
                  <td class="py-2 text-center font-semibold">
                    <span :class="statusClass(payment.statusCode)">
                      {{ statusText(payment.statusCode) }}
                    </span>
                  </td>
                  <td class="py-2 text-center">
                    {{ payment?.transactionId }}
                  </td>
                  <td class="py-2 text-center">
                    {{ formatCurrency(payment?.totalAmount) }}
                  </td>
                  <td class="py-2 text-center">{{ payment?.paymentNote }}</td>
                  <td
                    v-if="payment?.statusCode === '1'"
                    class="py-2 text-center text-sm cursor-pointer text-primary underline"
                    @click="toogleConfirmPaymentPopup(payment)"
                  >
                    xác nhận
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Mobile List -->
            <div class="md:hidden flex flex-col gap-2">
              <div
                v-for="payment in listPayment"
                :key="payment.paymentId"
                class="p-3 border rounded-lg bg-gray-50"
              >
                <div class="flex justify-between text-sm font-semibold">
                  <span
                    >Thời gian: {{ formatTimestampV3(payment?.payDate) }}</span
                  >
                  <span :class="statusClass(payment.statusCode)">{{
                    statusText(payment.statusCode)
                  }}</span>
                </div>
                <div class="text-md text-gray-600">
                  Phương thức: {{ payment?.methodDescription }}
                </div>
                <div class="mt-1">
                  Mã giao dịch: {{ payment?.transactionId }}
                </div>
                <div class="mt-1 text-primary font-bold">
                  Số tiền: {{ formatCurrency(payment?.totalAmount) }}
                </div>
                <div v-if="payment?.note" class="text-xs text-gray-500">
                  Ghi chú: {{ payment?.note }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
const ModalConfirmPayment = defineAsyncComponent(
  () => import("~/components/Modal/ModalConfirmPayment.vue")
);
const props = defineProps(["orderDetail"]);
const isVisible = ref(false);
const listPayment = ref<any[]>([]);
const loading = ref(false);
const { paymentsByOrders, confirmPayment } = usePayment();
const isModalOpen = ref(false);
const paymentId = ref<any>("");
const orderStore = useOrderStore();
const handleConfirm = async (data: any) => {
  isModalOpen.value = false;
  await confirmPayment(
    paymentId.value?.paymentId,
    data.transactionCode,
    data?.note,
    data.employee
  );
  handleChangeStatus(paymentId.value?.paymentId, data?.note);
  await orderStore.updateOrder(props.orderDetail?.id);
};
const handleChangeStatus = (paymentId: string, note: string) => {
  listPayment.value = listPayment.value.map((p) =>
    p.paymentId === paymentId ? { ...p, statusCode: "0", paymentNote: note } : p
  );
};

const toogleConfirmPaymentPopup = (paymentValue: any) => {
  paymentId.value = paymentValue;
  isModalOpen.value = !isModalOpen.value;
};
const statusClass = (statusCode: string) => {
  return {
    "text-green-500": statusCode === "0",
    "text-red-500": statusCode === "-1",
    "text-yellow-500": statusCode === "1",
  };
};
const statusText = (statusCode: string) => {
  return statusCode === "0"
    ? "Thành công"
    : statusCode === "-1"
    ? "Thất bại"
    : "Đang chờ";
};
const getPayment = async () => {
  if (!props.orderDetail?.id || listPayment.value.length > 0) return;
  try {
    loading.value = true;
    const response = await paymentsByOrders([props.orderDetail.id]);
    listPayment.value = [...(response || [])].sort(
      (a, b) =>
        new Date(b.transactionDate).getTime() -
        new Date(a.transactionDate).getTime()
    );
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};
const toggleVisible = async () => {
  isVisible.value = !isVisible.value;
  if (isVisible.value) {
    await getPayment();
  }
};
</script>
