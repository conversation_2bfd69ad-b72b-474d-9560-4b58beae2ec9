<template>
  <div class="p-2 md:p-4 h-full-custom2">
    <div class="grid grid-cols-1 gap-2" v-if="products?.length > 0">
      <OrderItemInventory
        v-for="product in dataProduct"
        :key="product.id"
        :product="product.orderLineItem"
      ></OrderItemInventory>
    </div>

    <div v-else class="lg:h-full h-[210px] overflow-auto hidden md:block">
      <div class="my-3">
        <div class="flex items-center justify-center">
          <img
            src="@/assets/images/cartEmpty.svg"
            class="w-32"
            loading="lazy"
          />
        </div>
        <div class="mt-2 text-sm font-semibold text-center text-textBlack">
          Bạn chưa có sản phẩm nào trong giỏ hàng!
        </div>
      </div>
    </div>
    <div
      v-if="route.params.id"
      class="fixed bottom-0 left-0 w-full bg-white shadow-lg z-20"
    >
      <div class="w-full bg-[#1565C0] py-1 flex justify-center items-center">
        <div
          @click="handleCreateOrder"
          class="bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark"
        >
          Tạo đơn hàng
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const OrderItemInventory = defineAsyncComponent(() =>
  import("~/components/Order/OrderItemInventory.vue")
);
const route = useRoute();

const orderStore = useOrderStore();
const products = computed(
  () => orderStore.orderDetail?.activeOrderItemProfiles
);
const dataProduct = computed(() => orderStore.dataInventory);
const { isTrue } = defineProps(["isTrue"]);
const handleItem = (item) => {
  dataRemove.value.push(item);
};
const handleRemoveProduct = (item) => {
  dataRemove.value = dataRemove.value.filter((data) => data.id !== item.id);
};
onMounted(() => {
  orderStore.disabledProducts = [];
});
</script>
<style scoped>
/* .h-full-custom2 {
      height: calc(100vh - 20rem);
    } */
</style>
