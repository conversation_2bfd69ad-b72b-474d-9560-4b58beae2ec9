# Navigation Components

This directory contains navigation-related UI components.

## Pagination Component

A comprehensive, reusable pagination component with full accessibility support and customizable styling.

### Features

- ✅ **Responsive Design**: Mobile-first approach with different layouts for mobile and desktop
- ✅ **Accessibility**: Full ARIA support and keyboard navigation
- ✅ **Customizable Sizes**: Small, medium, and large variants
- ✅ **Page Size Selection**: Optional dropdown to change items per page
- ✅ **Smart Page Display**: Shows ellipsis for large page counts
- ✅ **First/Last Navigation**: Optional first and last page buttons
- ✅ **Keyboard Support**: Arrow keys, Home, and End key navigation
- ✅ **TypeScript Support**: Full type safety with interfaces

### Usage

```vue
<template>
  <Pagination
    :current-page="pagination.currentPage"
    :total-pages="pagination.totalPages"
    :total-items="pagination.totalItems"
    :items-per-page="pagination.itemsPerPage"
    :show-page-size-selector="true"
    :page-size-options="[10, 20, 50]"
    size="md"
    @page-change="handlePageChange"
    @page-size-change="handlePageSizeChange"
  />
</template>

<script setup>
const pagination = reactive({
  currentPage: 1,
  totalPages: 10,
  totalItems: 100,
  itemsPerPage: 10,
})

const handlePageChange = (page) => {
  pagination.currentPage = page
  // Fetch new data
}

const handlePageSizeChange = (size) => {
  pagination.itemsPerPage = size
  pagination.currentPage = 1 // Reset to first page
  // Fetch new data
}
</script>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `currentPage` | `number` | Required | Current active page |
| `totalPages` | `number` | Required | Total number of pages |
| `totalItems` | `number` | Required | Total number of items |
| `itemsPerPage` | `number` | Required | Number of items per page |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Component size variant |
| `showFirstLast` | `boolean` | `true` | Show first/last page buttons |
| `showPageSizeSelector` | `boolean` | `false` | Show page size dropdown |
| `pageSizeOptions` | `number[]` | `[10, 20, 50, 100]` | Available page size options |
| `maxVisiblePages` | `number` | `7` | Maximum visible page numbers |

### Events

| Event | Payload | Description |
|-------|---------|-------------|
| `page-change` | `number` | Emitted when page changes |
| `page-size-change` | `number` | Emitted when page size changes |

### Keyboard Navigation

- **Arrow Left**: Previous page
- **Arrow Right**: Next page
- **Home**: First page
- **End**: Last page

### Accessibility Features

- ARIA labels for all interactive elements
- Proper role and navigation attributes
- Keyboard navigation support
- Screen reader friendly
- Focus management

### Styling

The component uses Tailwind CSS with a primary color theme. The `bg-primary`, `border-primary`, and `text-primary` classes should be defined in your Tailwind configuration.

### Size Variants

- **Small (`sm`)**: Compact design for tight spaces
- **Medium (`md`)**: Default size for most use cases
- **Large (`lg`)**: Larger design for emphasis

### Mobile Responsiveness

On mobile devices, the component shows a simplified view with:
- Previous/Next buttons
- Current page indicator
- Page size selector (if enabled)

On desktop, it shows the full pagination with:
- Results information
- Page numbers with ellipsis
- First/Last buttons (if enabled)
- Page size selector (if enabled)
