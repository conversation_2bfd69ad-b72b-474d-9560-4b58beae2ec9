<template>
  <nav
    :class="['flex items-center justify-between', sizeClasses[size].container]"
    role="navigation"
    aria-label="Pagination Navigation"
  >
    <!-- Mobile pagination info -->
    <div class="flex flex-1 justify-between sm:hidden">
      <button
        @click="goToPrevious"
        :disabled="currentPage <= 1"
        :class="[
          'relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
          currentPage <= 1
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-50',
        ]"
        :aria-label="'Go to previous page'"
      >
        Trước
      </button>
      <span
        class="relative inline-flex items-center px-4 py-2 text-sm text-gray-700"
      >
        Trang {{ currentPage }} / {{ totalPages }}
      </span>
      <button
        @click="goToNext"
        :disabled="currentPage >= totalPages"
        :class="[
          'relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
          currentPage >= totalPages
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-50',
        ]"
        :aria-label="'Go to next page'"
      >
        Sau
      </button>
    </div>

    <!-- Desktop pagination -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <!-- Results info -->
      <div>
        <p :class="['text-gray-700', sizeClasses[size].text]">
          Hiển thị
          <span class="font-medium">{{ startItem }}</span>
          đến
          <span class="font-medium">{{ endItem }}</span>
          trong tổng số
          <span class="font-medium">{{ totalItems }}</span>
          kết quả
        </p>
      </div>

      <!-- Page numbers -->
      <div>
        <nav
          class="isolate inline-flex -space-x-px rounded-md shadow-sm"
          aria-label="Pagination"
        >
          <!-- First page button -->
          <button
            v-if="showFirstLast && currentPage > 3"
            @click="goToPage(1)"
            :class="[
              'relative inline-flex items-center rounded-l-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
              sizeClasses[size].button,
            ]"
            aria-label="Go to first page"
          >
            <ChevronDoubleLeftIcon
              :class="sizeClasses[size].icon"
              aria-hidden="true"
            />
          </button>

          <!-- Previous button -->
          <button
            @click="goToPrevious"
            :disabled="currentPage <= 1"
            :class="[
              'relative inline-flex items-center border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
              currentPage <= 1
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-gray-50',
              !showFirstLast || currentPage <= 3 ? 'rounded-l-md' : '',
              sizeClasses[size].button,
            ]"
            aria-label="Go to previous page"
          >
            <ChevronLeftIcon
              :class="sizeClasses[size].icon"
              aria-hidden="true"
            />
          </button>

          <!-- Page numbers -->
          <template v-for="page in visiblePages" :key="page">
            <button
              v-if="typeof page === 'number'"
              @click="goToPage(page)"
              :class="[
                'relative inline-flex items-center border text-sm font-medium focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
                page === currentPage
                  ? 'z-10 bg-primary border-primary text-white'
                  : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50',
                sizeClasses[size].button,
              ]"
              :aria-label="`Go to page ${page}`"
              :aria-current="page === currentPage ? 'page' : undefined"
            >
              {{ page }}
            </button>
            <span
              v-else
              :class="[
                'relative inline-flex items-center border border-gray-300 bg-white text-gray-700',
                sizeClasses[size].button,
              ]"
            >
              ...
            </span>
          </template>

          <!-- Next button -->
          <button
            @click="goToNext"
            :disabled="currentPage >= totalPages"
            :class="[
              'relative inline-flex items-center border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
              currentPage >= totalPages
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-gray-50',
              !showFirstLast || currentPage >= totalPages - 2
                ? 'rounded-r-md'
                : '',
              sizeClasses[size].button,
            ]"
            aria-label="Go to next page"
          >
            <ChevronRightIcon
              :class="sizeClasses[size].icon"
              aria-hidden="true"
            />
          </button>

          <!-- Last page button -->
          <button
            v-if="showFirstLast && currentPage < totalPages - 2"
            @click="goToPage(totalPages)"
            :class="[
              'relative inline-flex items-center rounded-r-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 focus:z-20 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
              sizeClasses[size].button,
            ]"
            aria-label="Go to last page"
          >
            <ChevronDoubleRightIcon
              :class="sizeClasses[size].icon"
              aria-hidden="true"
            />
          </button>
        </nav>
      </div>
    </div>

    <!-- Page size selector -->
    <div v-if="showPageSizeSelector" class="ml-4">
      <select
        :value="itemsPerPage"
        @change="handlePageSizeChange"
        :class="[
          'rounded-md border border-gray-300 bg-white text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary',
          sizeClasses[size].select,
        ]"
        aria-label="Items per page"
      >
        <option v-for="size in pageSizeOptions" :key="size" :value="size">
          {{ size }} / trang
        </option>
      </select>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from "vue";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
} from "@heroicons/vue/24/outline";

interface Props {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  size?: "sm" | "md" | "lg";
  showFirstLast?: boolean;
  showPageSizeSelector?: boolean;
  pageSizeOptions?: number[];
  maxVisiblePages?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: "md",
  showFirstLast: true,
  showPageSizeSelector: false,
  pageSizeOptions: () => [10, 20, 50, 100],
  maxVisiblePages: 7,
});

interface Emits {
  (e: "page-change", page: number): void;
  (e: "page-size-change", size: number): void;
}

const emit = defineEmits<Emits>();

// Size classes for different component sizes
const sizeClasses = {
  sm: {
    container: "px-2 py-2",
    button: "px-2 py-1 text-xs",
    icon: "h-3 w-3",
    text: "text-xs",
    select: "px-2 py-1 text-xs",
  },
  md: {
    container: "px-4 py-3",
    button: "px-3 py-2 text-sm",
    icon: "h-4 w-4",
    text: "text-sm",
    select: "px-3 py-2 text-sm",
  },
  lg: {
    container: "px-6 py-4",
    button: "px-4 py-2 text-base",
    icon: "h-5 w-5",
    text: "text-base",
    select: "px-4 py-2 text-base",
  },
};

// Computed properties
const startItem = computed(() => {
  return (props.currentPage - 1) * props.itemsPerPage + 1;
});

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems);
});

const visiblePages = computed(() => {
  const pages: (number | string)[] = [];
  const total = props.totalPages;
  const current = props.currentPage;
  const maxVisible = props.maxVisiblePages;

  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    // Calculate start and end of visible range
    let start = Math.max(2, current - Math.floor((maxVisible - 3) / 2));
    let end = Math.min(total - 1, start + maxVisible - 4);

    // Adjust start if end is at the limit
    if (end === total - 1) {
      start = Math.max(2, end - maxVisible + 4);
    }

    // Add ellipsis before start if needed
    if (start > 2) {
      pages.push("...");
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Add ellipsis after end if needed
    if (end < total - 1) {
      pages.push("...");
    }

    // Always show last page
    if (total > 1) {
      pages.push(total);
    }
  }

  return pages;
});

// Methods
const goToPage = (page: number) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit("page-change", page);
  }
};

const goToPrevious = () => {
  if (props.currentPage > 1) {
    goToPage(props.currentPage - 1);
  }
};

const goToNext = () => {
  if (props.currentPage < props.totalPages) {
    goToPage(props.currentPage + 1);
  }
};

const handlePageSizeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  const newSize = parseInt(target.value);
  emit("page-size-change", newSize);
};

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case "ArrowLeft":
      event.preventDefault();
      goToPrevious();
      break;
    case "ArrowRight":
      event.preventDefault();
      goToNext();
      break;
    case "Home":
      event.preventDefault();
      goToPage(1);
      break;
    case "End":
      event.preventDefault();
      goToPage(props.totalPages);
      break;
  }
};

// Add keyboard event listener
onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>
