<template>
  <div class="bg-white p-2 rounded text-sm">
    <div
      class="flex items-center justify-between cursor-pointer"
      @click="handleOpenMore"
    >
      <h3 class="text-primary font-semibold text-sm">Tùy chỉnh thông tin đơn hàng</h3>
      <div>
        <span v-if="!isOpen">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.0"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m19.5 8.25-7.5 7.5-7.5-7.5"
            />
          </svg>
        </span>
        <span v-else>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.0"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m4.5 15.75 7.5-7.5 7.5 7.5"
            />
          </svg>
        </span>
      </div>
    </div>
    <div v-if="isOpen">
      <div class="space-y-3 mt-3">
        <div class="flex items-center gap-2">
          <label for="employee-select" class="block w-40">Nhân viên</label>
          <select
            id="employee-select"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="selectedEmployee"
            @change="handleEmployeeChange"
            :disabled="
              !orderStore.orderDetail ||
              orderStore.orderDetail?.order?.customAttribute
                ?.exportVatInvoiceStatus === 'INVOICE_PUBLISHED'
            "
          >
            <option
              v-for="employee in dataEmployee"
              :key="employee.id"
              :value="employee.id"
            >
              {{ employee.name }}
            </option>
          </select>
        </div>
        <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-40">Kho</label>
          <select
            id="warehouse-select"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="selectedWarehouse"
            @change="handleWarehouseChange"
            :disabled="
              !orderStore.orderDetail ||
              orderStore.orderDetail?.order?.customAttribute
                ?.exportVatInvoiceStatus === 'INVOICE_PUBLISHED'
            "
          >
            <option
              v-for="warehouse in dataWarehouse"
              :key="warehouse.id"
              :value="warehouse.id"
            >
              {{ warehouse.name }}
            </option>
          </select>
        </div>
        <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-40">Thời gian</label>
          <input
            id="date-select"
            type="date"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="selectedDate"
            @change="handleDateChange"
            :disabled="
              !orderStore.orderDetail ||
              orderStore.orderDetail?.order?.customAttribute
                ?.exportVatInvoiceStatus === 'INVOICE_PUBLISHED'
            "
          />
        </div>
      </div>
    </div>
    <ConfirmDialog
      v-if="isEditOrderEmployee"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh nhân viên tạo đơn`"
      @confirm="confirmEmpolyee"
      @cancel="cancelEmployee"
    ></ConfirmDialog>
    <ConfirmDialog
      v-if="isEditOrderWarehouse"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh kho lấy hàng`"
      @confirm="confirmWarehouse"
      @cancel="cancelWarehouse"
    ></ConfirmDialog>
    <ConfirmDialog
      v-if="isEditOrderTime"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh thời gian tạo đơn`"
      @confirm="confirmTime"
      @cancel="cancelTime"
    ></ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
// Lazy load heavy components
const ConfirmDialog = defineAsyncComponent(
  () => import("~/components/dialog/ConfirmDialog.vue")
);
import type { Auth } from "~/types/Auth";

const isOpen = ref<Boolean>(true);
const dataEmployee = ref<any[]>([]);
const selectedEmployee = ref<string>("");
const selectedWarehouse = ref<string>("");

const { fetchDataEmployees, updateDateCreateOrder } = useOrder();
const { getInforWarehouse } = useWarehouse();
const orderStore = useOrderStore();

const order = computed(() => orderStore.orderDetail?.order);
const warehouseIdInOrder = computed(() => {
  orderStore.orderDetail?.order;
});
const warehouseId = useCookie("warehouseId");
const dataWarehouse = ref<any>([]);
const warehouse = useCookie("warehouse");
//
const selectedDate = ref<string>("");
const preSelectedDate = ref();
const isEditOrderTime = ref(false);
const handleDateChange = async () => {
  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderTime.value = true;
  } else {
    const selected = new Date(selectedDate.value);
    const now = new Date();
    selected.setHours(now.getHours(), now.getMinutes(), now.getSeconds());
    const timestamp = selected.getTime();
    await updateDateCreateOrder(route.query.orderId as string, timestamp, "");
    preSelectedDate.value = timestamp;
  }
};

const confirmTime = async () => {
  const timestamp = new Date(selectedDate.value).getTime();
  await updateDateCreateOrder(route.query.orderId as string, timestamp, "");
  preSelectedDate.value = timestamp;
  isEditOrderTime.value = false;
};
const cancelTime = () => {
  selectedDate.value = preSelectedDate.value;
  isEditOrderTime.value = false;
};
//
const handleOpenMore = () => {
  isOpen.value = !isOpen.value;
};
import { useMyEmployeeStore } from "@/stores/employee";
import { storeToRefs } from "pinia";

const employeeStore = useMyEmployeeStore();
const { employees } = storeToRefs(employeeStore); // Để đảm bảo reactive

const handleDataEmployee = async () => {
  try {
    const response = await fetchDataEmployees();
    dataEmployee.value = response;
    employees.value = response;
  } catch (error) {
    throw error;
  }
};

const route = useRoute();
const auth = useCookie("auth").value as unknown as Auth;
const isEditOrderEmployee = ref(false);
const orderDetail = computed(() => orderStore.orderDetail);
const preSelectedEmployee = ref();
const handleEmployeeChange = async (event: Event) => {
  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderEmployee.value = true;
  } else {
    const selectedEmployeeId = (event.target as HTMLSelectElement).value;
    await orderStore.handleUpdateSaleEmployee(
      route.query.orderId as string,
      selectedEmployeeId,
      auth.user.id
    );
    preSelectedEmployee.value = selectedEmployeeId;
  }
};
const confirmEmpolyee = async () => {
  await orderStore.handleUpdateSaleEmployee(
    route.query.orderId as string,
    selectedEmployee.value,
    auth.user.id
  );
  preSelectedEmployee.value = selectedEmployee.value;
  isEditOrderEmployee.value = false;
};
const cancelEmployee = () => {
  selectedEmployee.value = preSelectedEmployee.value;
  isEditOrderEmployee.value = false;
};
////
const isEditOrderWarehouse = ref(false);
const preSelectedWarehouse = ref();
const handleWarehouseChange = async (event: Event) => {
  const selectedWarehouseId = (event.target as HTMLSelectElement).value;

  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderWarehouse.value = true;
  } else {
    await orderStore.handleUpdateWarehouse(
      route.query.orderId as string,
      selectedWarehouseId
    );
    await orderStore.getOrderByIdV2(
      route.query.orderId as string,
      selectedWarehouseId
    );
    preSelectedWarehouse.value = selectedWarehouseId;
  }
};
const confirmWarehouse = async () => {
  await orderStore.handleUpdateWarehouse(
    route.query.orderId as string,
    selectedWarehouse.value
  );
  await orderStore.getOrderByIdV2(
    route.query.orderId as string,
    selectedWarehouse.value
  );
  preSelectedWarehouse.value = selectedWarehouse.value;
  isEditOrderWarehouse.value = false;
};
const cancelWarehouse = () => {
  selectedWarehouse.value = preSelectedWarehouse.value;
  isEditOrderWarehouse.value = false;
};
///
const handleInfoWarehouse = (warehouse: any) => {
  warehouse?.forEach(async (item: string) => {
    const response = await getInforWarehouse(item);
    dataWarehouse.value.push(response);
  });
};

onMounted(async () => {
  const listWarehouse = useCookie("warehouse").value;
  handleInfoWarehouse(listWarehouse);
  await handleDataEmployee();
});
watch(warehouse, (newVal, oldVal) => {
  dataWarehouse.value = [];
  handleInfoWarehouse(newVal);
});
watch(
  order,
  (newSalePartyId) => {
    selectedEmployee.value = newSalePartyId?.salePartyId;
    preSelectedEmployee.value = newSalePartyId?.salePartyId;
    selectedWarehouse.value = newSalePartyId?.customAttribute?.facilityId;
    preSelectedWarehouse.value = newSalePartyId?.customAttribute?.facilityId;

    // Handle date formatting with validation
    if (newSalePartyId?.orderDate) {
      const formattedDate = formatTimestampV4(newSalePartyId.orderDate);
      if (formattedDate !== "N/A") {
        selectedDate.value = formattedDate;
        preSelectedDate.value = newSalePartyId.orderDate;
      } else {
        selectedDate.value = "";
        preSelectedDate.value = null;
      }
    } else {
      selectedDate.value = "";
      preSelectedDate.value = null;
    }
  },
  { immediate: true }
);

watch(
  () => route.query.orderId,
  (newId, oldId) => {
    if (newId !== oldId) {
      isOpen.value = false;
    }
  }
);
</script>
