<template>
  <td class="px-2 py-1 cursor-pointer text-center border-r border-dashed">
    <div>
      <div class="flex items-center justify-center gap-1">
        <div
          @click="handleNavigate"
          class="underline decoration-primary decoration-[1px] text-primary"
        >
          {{ diary?.id }}
        </div>
        <div @click="handleCopy">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-3"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
            />
          </svg>
        </div>
      </div>
      <div class="flex items-center justify-center">
        <div :class="getOrderStatusClass(diary?.status)">
          {{ diary?.status === "DRAFT" ? "Nhật ký" : diary?.statusDescription }}
        </div>
        <span
          v-if="diary?.status === 'CANCELLED'"
          v-tippy="`${diary?.order?.note}`"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
            />
          </svg>
        </span>
      </div>
      <div>{{ formatTimestampV7(diary?.order?.orderDate) }}</div>
      <div v-if="diary?.order?.customAttribute?.subType">
        <div
          v-if="diary?.order?.customAttribute?.subType === 'RETURN_FULL'"
          class="text-red-400"
        >
          Trả toàn bộ
        </div>
        <div
          v-else-if="
            diary?.order?.customAttribute?.subType === 'EXCHANGE_ORDER'
          "
          class="text-primary"
        >
          Đơn đổi
        </div>
        <div v-else class="text-orange-300">Trả một phần</div>
      </div>
      <div
        v-if="isFFM && diary?.order?.fulfillmentStatus"
        class="flex items-center justify-center gap-1"
      >
        <span v-tippy="getFFMStatusText(diary?.order?.fulfillmentStatus)">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
            />
          </svg>
        </span>
        <span
          :class="getFFMStatusClass(diary?.order?.fulfillmentStatus)"
          class="truncate"
        >
          {{ getFFMStatusText(diary?.order?.fulfillmentStatus) }}
        </span>
      </div>
    </div>
  </td>
  <!-- Khách hàng -->
  <td v-if="!isDetailCustomerOrder" class="px-2 py-1 border-r border-dashed">
    <div class="flex items-center gap-1">
      <span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
          />
        </svg>
      </span>
      <span>{{ diary?.order?.ownerName }}</span>
    </div>
    <div class="flex items-center gap-1">
      <span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
          />
        </svg>
      </span>
      <span>{{ diary?.order?.ownerPhone }}</span>
    </div>
  </td>
  <!-- nhật ký -->
  <td v-if="!isNotDraft" class="px-2 py-1 border-r border-dashed">
    <div v-for="connector in connectors">
      <TagDiary :connector="connector"></TagDiary>
    </div>
  </td>
  <!-- nhân viên -->
  <td v-if="!isAgency" class="px-2 py-1 border-r border-dashed">
    <div class="space-x-1">
      <span class="font-semibold">Tạo:</span>
      <span> {{ employeeSale }}</span>
    </div>
    <div class="space-x-1">
      <span class="font-semibold">Tư vấn:</span>
      <span> {{ employee }}</span>
    </div>
  </td>
  <!-- sản phẩm -->
  <td class="px-2 py-1 border-r border-dashed max-w-0 overflow-hidden">
    <div class="space-y-1">
      <div
        class="text-gray-700 text-sm"
        v-for="product in isNotDraft
          ? diary.activeOrderItemProfiles
          : diary.orderItemProfiles"
        :key="product.id"
      >
        <ProductSimpleCard :product="product.orderLineItem"></ProductSimpleCard>
      </div>
    </div>
  </td>
  <!-- tổng giảm -->
  <td v-if="isNotDraft" class="px-2 py-1 border-r border-dashed">
    {{
      formatCurrency(
        diary?.order?.currentSubtotalPrice?.amount -
          diary.order?.discountTotalPrice?.amount || 0
      )
    }}
  </td>
  <!-- thanh toán -->
  <td v-if="isNotDraft" class="px-2 py-1 border-r border-dashed">
    <div>
      <span>Tổng tiền: </span>
      <span class="text-primary"> {{ formattedTotalPrice }}</span>
    </div>
    <div>
      <span>Đã thanh toán: </span>
      <span class="text-green-500">
        {{ formatCurrency(diary?.totalAlreadyPaid || 0) }}</span
      >
    </div>

    <div>
      <span>Còn nợ: </span>
      <span class="text-red-500">
        {{ formatCurrency(diary?.remainTotal || 0) }}</span
      >
    </div>
    <div
      v-if="
        diary?.order?.customAttribute?.exportVatInvoiceStatus ===
        'INVOICE_PUBLISHED'
      "
      class="cursor-pointer"
      @click="toogleExportInvoice"
    >
      <span>Mã hóa đơn: </span>
      <span>{{ numberOfInvoice?.value }}</span>
    </div>
    <!-- <div>
      <span>Trạng thái: </span>
      <span :class="getPaymentStatusClass(diary?.financialStatusDescription)">
        {{ diary?.financialStatusDescription }}</span
      >
    </div> -->
  </td>
  <!-- hàng nút action -->
  <OptionSendZalo
    :diary="diary"
    :isNotDraft="isNotDraft"
    :data="data"
  ></OptionSendZalo>
  <ExportInvoicePopup
    v-if="isOpenExportInvoice"
    :order="diary"
    @confirm="toogleExportInvoice"
    @cancel="toogleExportInvoice"
  ></ExportInvoicePopup>
</template>

<script setup lang="ts">
const OptionSendZalo = defineAsyncComponent(
  () => import("~/components/ComHub/OptionSendZalo.vue")
);
const TagDiary = defineAsyncComponent(
  () => import("~/components/Diary/TagDiary.vue")
);
const ExportInvoicePopup = defineAsyncComponent(
  () => import("~/components/dialog/ExportInvoicePopup.vue")
);
const props = defineProps([
  "diary",
  "isNotDraft",
  "isAgency",
  "isDetailCustomerOrder",
  "isFFM",
  "data",
]);
const { searchEmployes } = useOrder();
const route = useRoute();
const formattedTotalPrice = computed(() =>
  formatCurrency(props.diary?.order?.totalPrice?.amount || 0)
);
const { getInvoicesOfOrder } = useInvoice();
const numberOfInvoice = ref();
const handleView = async () => {
  if (
    props.diary?.order?.customAttribute?.exportVatInvoiceStatus !==
    "INVOICE_PUBLISHED"
  ) {
    return;
  }
  try {
    const response = await getInvoicesOfOrder(props.diary?.id);
    if (response?.length >= 0) {
      numberOfInvoice.value = response[0]?.attributes?.find(
        (invoice: any) => invoice.name === "INV_NO"
      );
    }
  } catch (error) {
    throw error;
  }
};
/////
const ordersStore = useOrdersStore();
///

const employee = ref();
const employeeSale = ref();

const handleSearchEmployees = async (idEmployee: string) => {
  if (!idEmployee) return;
  // Lấy danh sách từ localStorage
  const existingList = JSON.parse(localStorage.getItem("listEmployee") || "[]");

  // Tìm trong danh sách nhân viên hiện tại
  const res = diaryStore.dataEmployee?.find(
    (item: any) => item.id === idEmployee
  );

  if (res) {
    // Nếu tìm thấy trong danh sách hiện tại, cập nhật giá trị
    employee.value = res?.name;
  } else {
    // Nếu không có trong danh sách nhân viên, kiểm tra trong localStorage
    const localEmployee = existingList.find(
      (item: any) => item.id === idEmployee
    );

    if (localEmployee) {
      // Nếu tìm thấy trong localStorage, cập nhật giá trị
      employee.value = localEmployee.name;
    } else {
      // Nếu không tìm thấy, gọi API để tìm kiếm
      const data: any = {
        keyword: idEmployee,
        positionShortName: "",
      };

      try {
        const res = await searchEmployes(data);

        if (res && res.length > 0) {
          // Cập nhật giá trị `employee` từ kết quả API
          employee.value = res[0]?.name;

          // Thêm kết quả mới vào danh sách `localStorage`
          existingList.push(res[0]);
          localStorage.setItem("listEmployee", JSON.stringify(existingList));
        }
      } catch (error) {
        console.error("Lỗi khi gọi API searchEmployes:", error);
      }
    }
  }
};

const diaryStore = useDiariesStore();

const handleSearchEmployeeSale = async (idEmployee: string) => {
  if (!idEmployee) return;
  // Lấy danh sách từ localStorage
  const existingList = JSON.parse(localStorage.getItem("listEmployee") || "[]");

  // Tìm trong danh sách nhân viên hiện tại
  const res = diaryStore.dataEmployee?.find(
    (item: any) => item.id === idEmployee
  );

  if (res) {
    // Nếu tìm thấy trong danh sách hiện tại, cập nhật giá trị
    employeeSale.value = res?.name;
  } else {
    // Nếu không có trong danh sách nhân viên, kiểm tra trong localStorage
    const localEmployee = existingList.find(
      (item: any) => item.id === idEmployee
    );

    if (localEmployee) {
      // Nếu tìm thấy trong localStorage, cập nhật giá trị
      employeeSale.value = localEmployee.name;
    } else {
      // Nếu không tìm thấy, gọi API để tìm kiếm
      const data: any = {
        keyword: idEmployee,
        positionShortName: "",
      };
      try {
        const res = await searchEmployes(data);
        if (res && res.length > 0) {
          // Cập nhật giá trị `employee` từ kết quả API
          employeeSale.value = res[0]?.name;

          // Thêm kết quả mới vào danh sách `localStorage`
          existingList.push(res[0]);
          localStorage.setItem("listEmployee", JSON.stringify(existingList));
        }
      } catch (error) {
        throw error;
      }
    }
  }
};
const { getConnectorByResource, getTags } = usePortal();
// const handleGetTag = async () => {
//   try {
//     const response = await getTags(props.tag?._id);
//     dataTag.value = response;
//   } catch (error) {
//     throw error;
//   }
// };
const connectors = ref();
const tags = ref<any>();
const handleGetDiary = async () => {
  if (!props.isNotDraft) {
    const response = await getConnectorByResource(
      props.diary?.id,
      "ORDER",
      "TAG"
    );
    connectors.value = response;
  }
};
onMounted(async () => {
  ordersStore.tooltip = null;
  await handleSearchEmployees(props.diary?.order?.salePartyId);
  await Promise.allSettled([
    handleSearchEmployeeSale(props.diary?.order?.createdBy),
    handleGetDiary(),
    handleView(),
  ]);
});
const emits = defineEmits(["handleLoading", "handleOpenDetailDialog"]);

const handleNavigate = async () => {
  if (props.isDetailCustomerOrder) {
    emits("handleOpenDetailDialog", props.diary);
    return;
  }
  // đơn đổi
  if (props.diary?.order?.orderGroup === "RETURN_ORDER") {
    await navigateTo(
      `/order/return/detail?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
    return;
  }
  // đơn nháp
  if (props.diary.status === "DRAFT") {
    await navigateTo(
      `/diary?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
  } else {
    // đơn bán
    await navigateTo(
      `/sale?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
  }
};

// Import utilities
import {
  getOrderStatusClass,
  getPaymentStatusClass,
  getFFMStatusClass,
  getFFMStatusText,
} from "~/utils/statusHelpers";
const isOpenExportInvoice = ref(false);
const toogleExportInvoice = () => {
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
const handleCopy = async () => {
  await navigator.clipboard.writeText(props.diary?.id);
  useNuxtApp().$toast.success("Đã lưu mã đơn vào clipboard");
};
</script>

<style scoped>
.disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>
